/* ===== NEXUS ODOO-INSPIRED DESIGN SYSTEM ===== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* ===== CSS VARIABLES ===== */
:root {
    /* Primary Colors */
    --nexus-primary: #0066FF;
    --nexus-primary-dark: #0052CC;
    --nexus-secondary: #6366F1;
    --nexus-success: #10B981;
    --nexus-warning: #F59E0B;
    --nexus-danger: #EF4444;

    /* Gray Scale */
    --gray-50: #F8FAFC;
    --gray-100: #F1F5F9;
    --gray-200: #E2E8F0;
    --gray-300: #CBD5E1;
    --gray-400: #94A3B8;
    --gray-500: #64748B;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1E293B;
    --gray-900: #0F172A;

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: all 150ms ease-in-out;
    --transition-normal: all 300ms ease-in-out;
}

/* ===== BASE STYLES ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--gray-700);
    background-color: #ffffff;
    margin: 0;
    padding: 0;
}

/* ===== HEADER STYLES ===== */
.header {
    background: #ffffff;
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-fast);
}

.header .navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--nexus-primary) !important;
    text-decoration: none;
}

.header .navbar-nav .nav-link {
    font-weight: 500;
    color: var(--gray-600) !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.header .navbar-nav .nav-link:hover {
    color: var(--nexus-primary) !important;
    background-color: var(--gray-50);
}

.header .btn-primary {
    background-color: var(--nexus-primary);
    border-color: var(--nexus-primary);
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: var(--radius-lg);
}

.header .btn-primary:hover {
    background-color: var(--nexus-primary-dark);
    border-color: var(--nexus-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* ===== HERO SECTION ===== */
.pt-5.pb-6 {
    padding-top: var(--spacing-20) !important;
    padding-bottom: var(--spacing-16) !important;
    background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
}

.display-1 {
    font-size: 4rem !important;
    font-weight: 800 !important;
    line-height: 1.1 !important;
    letter-spacing: -0.02em !important;
    color: var(--gray-900) !important;
    margin-bottom: var(--spacing-6) !important;
}

.display-3 {
    font-size: 2.5rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    color: var(--gray-600) !important;
}

.text-warning.fw-bold {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 900 !important;
}

.text-primary.fw-bold {
    color: var(--nexus-primary) !important;
    font-weight: 700 !important;
}

/* ===== APPS SECTION ===== */
.bg-light {
    background-color: var(--gray-100) !important;
    padding: var(--spacing-16) 0;
}

.apps-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-4);
}

.app-entry {
    text-decoration: none;
    transition: var(--transition-fast);
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
}

.app-entry:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.app-entry figure {
    margin: 0;
}

.app-entry .img-thumbnail {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg) !important;
    padding: var(--spacing-3);
    background: #ffffff;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.app-entry:hover .img-thumbnail {
    box-shadow: var(--shadow-md);
    border-color: var(--nexus-primary);
}

.app-entry figcaption {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: var(--gray-600) !important;
    margin-top: var(--spacing-2) !important;
}

/* ===== TECHNOLOGY DEVICES SECTION ===== */
.pt-7 {
    padding-top: var(--spacing-24) !important;
}

.pb-5 {
    padding-bottom: var(--spacing-20) !important;
}

.ratio.ratio-1x1 {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-4);
    background-color: var(--gray-100) !important;
    border: 1px solid var(--gray-200);
    transition: var(--transition-fast);
}

.ratio.ratio-1x1:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--nexus-primary);
}

.h5 {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: var(--gray-700) !important;
    margin-top: var(--spacing-3) !important;
}

/* ===== BUTTON STYLES ===== */
.btn {
    font-weight: 600;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--nexus-primary);
    border-color: var(--nexus-primary);
}

.btn-primary:hover {
    background-color: var(--nexus-primary-dark);
    border-color: var(--nexus-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-light {
    background-color: #ffffff;
    border-color: var(--gray-300);
    color: var(--gray-700);
}

.btn-light:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--gray-800);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.125rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .display-1 {
        font-size: 2.5rem !important;
    }

    .display-3 {
        font-size: 1.75rem !important;
    }

    .apps-grid {
        gap: var(--spacing-2);
    }

    .app-entry {
        padding: var(--spacing-2);
    }

    .app-entry .img-thumbnail {
        width: 60px !important;
    }

    .ratio.ratio-1x1 {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 576px) {
    .display-1 {
        font-size: 2rem !important;
    }

    .display-3 {
        font-size: 1.5rem !important;
    }

    .pt-5.pb-6 {
        padding-top: var(--spacing-12) !important;
        padding-bottom: var(--spacing-10) !important;
    }
}