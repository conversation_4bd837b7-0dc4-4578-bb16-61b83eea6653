/* ===== NEXUS ODOO-EXACT STYLING ===== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* ===== BASE STYLES ===== */
:root {
    --nexus-primary: #0066FF;
    --nexus-yellow: #FFD700;
    --nexus-green: #10B981;
    --nexus-red: #EF4444;
    --nexus-blue: #3B82F6;
    --gray-50: #F8FAFC;
    --gray-100: #F1F5F9;
    --gray-200: #E2E8F0;
    --gray-300: #CBD5E1;
    --gray-400: #94A3B8;
    --gray-500: #64748B;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1E293B;
    --gray-900: #0F172A;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--gray-700);
    background-color: #ffffff;
    margin: 0;
    padding: 0;
}

/* ===== HEADER STYLES ===== */
.navbar {
    background-color: #ffffff !important;
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 800 !important;
    font-size: 1.75rem !important;
    color: var(--nexus-primary) !important;
}

.nav-link {
    font-weight: 500 !important;
    color: var(--gray-600) !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.nav-link:hover {
    color: var(--nexus-primary) !important;
    background-color: var(--gray-50);
}

.btn-primary {
    background-color: var(--nexus-primary) !important;
    border-color: var(--nexus-primary) !important;
    font-weight: 600 !important;
    padding: 0.5rem 1.5rem !important;
    border-radius: 0.5rem !important;
}

.btn-primary:hover {
    background-color: #0052CC !important;
    border-color: #0052CC !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* ===== HERO SECTION ===== */
.pt-5.pb-6 {
    padding-top: 5rem !important;
    padding-bottom: 4rem !important;
    background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%);
}

.display-1 {
    font-size: 4rem !important;
    font-weight: 800 !important;
    line-height: 1.1 !important;
    letter-spacing: -0.02em !important;
    color: var(--gray-900) !important;
    margin-bottom: 1.5rem !important;
}

.x_wd_yellow_highlight_bold_05 {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 900 !important;
}

.display-3 {
    font-size: 2.5rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    color: var(--gray-600) !important;
}

.x_wd_blue_highlight_01 {
    color: var(--nexus-primary) !important;
    font-weight: 700 !important;
}

.x_wd_doodle {
    transform: rotate(-10deg);
    margin-top: -12px !important;
    margin-right: -80px !important;
}

.x_wd_doodle img {
    filter: drop-shadow(0 2px 4px rgba(0, 102, 255, 0.2));
}

.x_wd_doodle span {
    color: var(--nexus-primary) !important;
    font-weight: 700 !important;
    font-size: 0.875rem !important;
    text-shadow: 0 2px 4px rgba(0, 102, 255, 0.1);
}

/* ===== APPS SECTION ===== */
.bg-200 {
    background-color: var(--gray-100) !important;
}

.o_colored_level {
    padding: 4rem 0 !important;
}

.pt-4.pt-md-6 {
    padding-top: 2rem !important;
}

@media (min-width: 768px) {
    .pt-4.pt-md-6 {
        padding-top: 4rem !important;
    }
}

.pb-7 {
    padding-bottom: 5rem !important;
}

.x_wd_app_entry {
    text-decoration: none;
    transition: all 0.15s ease-in-out;
    padding: 0.75rem;
    border-radius: 0.5rem;
}

.x_wd_app_entry:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.x_wd_app_entry figure {
    margin: 0;
}

.x_wd_app_entry .img-thumbnail {
    border: 1px solid var(--gray-200) !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem !important;
    background: #ffffff !important;
    transition: all 0.15s ease-in-out;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.x_wd_app_entry:hover .img-thumbnail {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-color: var(--nexus-primary) !important;
}

.x_wd_app_entry figcaption {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: var(--gray-600) !important;
    margin-top: 0.5rem !important;
}

.text-o-color-5 {
    color: var(--gray-600) !important;
}

/* ===== TECHNOLOGY DEVICES SECTION ===== */
.pt-7 {
    padding-top: 6rem !important;
}

.pb-5 {
    padding-bottom: 5rem !important;
}

.display-2 {
    font-size: 3.5rem !important;
    font-weight: 800 !important;
    line-height: 1.1 !important;
    color: var(--gray-900) !important;
    margin-bottom: 3rem !important;
}

.x_wd_blue_highlight_bold_03 {
    color: var(--nexus-primary) !important;
    font-weight: 900 !important;
}

.ratio.ratio-1x1 {
    width: 140px;
    height: 140px;
    margin: 0 auto 1rem;
    background-color: var(--gray-100) !important;
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease-in-out;
    overflow: hidden;
}

.ratio.ratio-1x1:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: var(--nexus-primary);
    background-color: #ffffff !important;
}

.ratio.ratio-1x1 img {
    transition: all 0.3s ease-in-out;
    filter: grayscale(20%);
}

.ratio.ratio-1x1:hover img {
    filter: grayscale(0%);
    transform: scale(1.1);
}

.h5 {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: var(--gray-700) !important;
    margin-top: 1rem !important;
    transition: all 0.15s ease-in-out;
}

.col-6:hover .h5,
.col-lg-3:hover .h5 {
    color: var(--nexus-primary) !important;
}

/* ===== DROPDOWN STYLES ===== */
.dropdown-menu {
    border: 1px solid var(--gray-200);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.dropdown-header {
    font-weight: 700 !important;
    color: var(--gray-900) !important;
    font-size: 0.875rem !important;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.75rem !important;
}

.dropdown-item {
    padding: 0.5rem 0 !important;
    font-size: 0.875rem !important;
    color: var(--gray-600) !important;
    border: none !important;
    background: none !important;
    transition: all 0.15s ease-in-out;
}

.dropdown-item:hover {
    color: var(--nexus-primary) !important;
    background: none !important;
    padding-left: 0.5rem !important;
}

/* ===== BUTTON STYLES ===== */
.btn {
    font-weight: 600;
    border-radius: 0.5rem;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
}

.btn-light {
    background-color: #ffffff;
    border-color: var(--gray-300);
    color: var(--gray-700);
}

.btn-light:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--gray-800);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.125rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .display-1 {
        font-size: 2.5rem !important;
    }
    
    .display-2 {
        font-size: 2.5rem !important;
    }
    
    .display-3 {
        font-size: 1.75rem !important;
    }
    
    .ratio.ratio-1x1 {
        width: 100px;
        height: 100px;
    }
    
    .pt-5.pb-6 {
        padding-top: 3rem !important;
        padding-bottom: 2rem !important;
    }
}

@media (max-width: 576px) {
    .display-1 {
        font-size: 2rem !important;
    }
    
    .display-2 {
        font-size: 2rem !important;
    }
    
    .display-3 {
        font-size: 1.5rem !important;
    }
}
