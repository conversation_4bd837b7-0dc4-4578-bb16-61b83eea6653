2025-06-15 10:58:02,555 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-15 14:17:56,620 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-15 15:01:35,153 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-15 15:01:35,162 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-15 15:01:35,170 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-15 15:01:35,181 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-15 15:01:35,197 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-15 15:01:35,315 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-15 15:01:35,322 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-15 15:01:35,328 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-15 15:01:35,333 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-15 15:01:35,337 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-15 15:01:35,341 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-15 15:01:35,346 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-15 15:01:35,354 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-15 15:01:35,361 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-15 15:01:35,380 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-15 15:01:35,387 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-15 15:01:35,394 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-15 16:29:24,809 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-15 16:29:25,071 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-15 16:29:25,076 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-15 16:31:27,105 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-15 16:31:27,107 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-15 16:31:27,117 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-15 16:31:27,119 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-15 16:31:27,121 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-15 16:31:27,124 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-15 16:31:27,191 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-15 16:31:27,193 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-15 16:31:27,197 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-15 16:31:27,257 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-15 16:31:27,259 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-15 16:31:27,261 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-15 16:31:27,265 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-15 16:32:28,652 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-15 16:32:28,656 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-15 16:32:28,670 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-15 16:32:28,674 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-15 16:32:28,677 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-15 16:32:28,679 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-15 16:32:28,771 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-15 16:32:28,774 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-15 16:32:28,778 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-15 16:32:28,850 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-15 16:32:28,853 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-15 16:32:28,857 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-15 16:32:28,860 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-15 16:33:29,802 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-15 16:33:29,805 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-15 16:33:29,809 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-15 16:33:29,816 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-15 16:33:29,818 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-15 16:33:29,821 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-15 16:33:29,823 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-15 16:33:29,889 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-15 16:33:29,891 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-15 16:33:29,895 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-15 16:33:29,947 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-15 16:33:29,950 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-15 16:33:29,955 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-15 16:33:29,958 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-15 16:33:29,960 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-15 16:33:29,962 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-15 16:34:31,023 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-15 16:34:31,027 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-15 16:34:31,033 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-15 16:34:31,042 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-15 16:34:31,045 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-15 16:34:31,047 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-15 16:34:31,050 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-15 16:34:31,140 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-15 16:34:31,142 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-15 16:34:31,146 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-15 16:34:31,202 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-15 16:34:31,205 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-15 16:34:31,211 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-15 16:34:31,214 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-15 16:34:31,216 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-15 16:34:31,218 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-15 16:37:34,233 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-15 16:37:34,373 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-15 16:37:34,375 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-15 16:38:35,690 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-15 16:38:35,831 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-15 16:38:35,833 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-15 16:39:36,107 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-15 16:39:36,299 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-15 16:39:36,301 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-16 10:57:55,788 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-16 10:57:55,796 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-16 10:57:55,801 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-16 10:57:55,806 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-16 10:57:55,813 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-06-16 10:57:55,816 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for child_ngo
2025-06-16 10:57:55,819 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-16 10:57:55,824 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-16 10:57:55,827 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-16 10:57:55,830 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-16 10:57:55,839 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for child_ngo
2025-06-16 10:57:55,843 ERROR scheduler Skipped queueing ngo.utils.loan.apply_late_payment_fees because it was found in queue for child_ngo
2025-06-16 10:57:55,846 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-06-16 10:57:55,851 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-16 10:57:55,856 ERROR scheduler Skipped queueing ngo.utils.attachments.set_attachments_status because it was found in queue for child_ngo
2025-06-16 10:57:55,865 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-16 10:57:55,872 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-06-16 10:57:55,877 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-06-16 10:57:55,880 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-06-16 10:57:55,884 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-06-16 10:57:55,889 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for child_ngo
2025-06-16 10:57:55,892 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for child_ngo
2025-06-16 10:57:55,894 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for child_ngo
2025-06-16 10:57:55,897 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for child_ngo
2025-06-16 10:57:55,900 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for child_ngo
2025-06-16 10:57:55,902 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for child_ngo
2025-06-16 10:57:55,906 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for child_ngo
2025-06-16 10:57:55,909 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for child_ngo
2025-06-16 10:57:55,911 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for child_ngo
2025-06-16 10:57:55,915 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for child_ngo
2025-06-16 10:57:55,918 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for child_ngo
2025-06-16 10:57:55,921 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for child_ngo
2025-06-16 10:57:55,924 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for child_ngo
2025-06-16 10:57:55,927 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for child_ngo
2025-06-16 10:57:55,930 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for child_ngo
2025-06-16 10:57:55,933 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for child_ngo
2025-06-16 10:57:55,936 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for child_ngo
2025-06-16 10:57:55,939 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for child_ngo
2025-06-16 10:57:55,942 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for child_ngo
2025-06-16 10:57:55,945 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for child_ngo
2025-06-16 10:57:55,948 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for child_ngo
2025-06-16 10:57:55,951 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-16 10:57:55,956 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-16 10:57:55,960 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-16 10:57:55,963 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-16 10:57:55,966 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-16 10:57:55,969 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-16 10:57:55,972 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for child_ngo
2025-06-16 10:57:55,976 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-16 10:57:56,001 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-06-16 10:57:56,004 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-06-16 10:57:56,008 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-16 10:57:56,012 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-16 10:57:56,015 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.set_auto_repeat_as_completed because it was found in queue for child_ngo
2025-06-16 10:57:56,018 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-06-16 10:57:56,023 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-06-16 10:57:56,026 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-06-16 10:57:56,029 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for child_ngo
2025-06-16 10:57:56,031 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-06-16 10:57:56,034 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for child_ngo
2025-06-16 10:57:56,037 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-06-16 10:57:56,041 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for child_ngo
2025-06-16 10:57:56,044 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-06-16 10:57:56,047 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-16 10:57:56,050 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-16 10:57:56,053 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-16 10:57:56,056 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-16 10:57:56,060 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-16 10:57:56,064 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-16 10:57:56,067 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-16 10:57:56,070 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-16 10:57:56,077 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-16 10:57:56,083 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-06-16 10:57:56,088 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-16 10:57:56,093 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-16 10:57:56,096 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-16 10:57:56,100 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-16 10:58:58,044 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-16 10:58:58,055 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-06-16 10:58:58,080 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-06-16 10:58:58,085 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-16 10:58:58,097 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-16 10:58:58,106 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-06-16 10:58:58,111 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-06-16 10:58:58,116 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-06-16 10:58:58,121 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-06-16 10:58:58,284 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-06-16 10:58:58,289 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-06-16 10:58:58,295 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-16 10:58:58,299 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-22 19:14:20,690 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-22 19:14:20,694 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-22 19:14:20,698 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-22 19:14:20,702 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-22 19:14:20,713 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-22 19:14:20,716 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-22 19:14:20,719 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-22 19:14:20,723 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-22 19:14:20,735 ERROR scheduler Skipped queueing ngo.utils.loan.apply_late_payment_fees because it was found in queue for child_ngo
2025-06-22 19:14:20,743 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-22 19:14:20,756 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-22 19:14:20,826 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-22 19:14:20,829 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-22 19:14:20,834 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-22 19:14:20,839 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-22 19:14:20,842 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-22 19:14:20,845 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-22 19:14:20,851 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-22 19:14:20,919 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-22 19:14:20,923 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-22 19:14:20,927 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-22 19:14:20,931 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-22 19:14:20,934 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-22 19:14:20,937 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-22 19:14:20,941 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-22 19:14:20,945 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-22 19:14:20,949 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-22 19:14:20,957 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-22 19:14:20,963 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-22 19:14:20,967 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-22 19:14:20,971 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-23 13:01:49,616 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-23 13:01:49,626 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-06-23 13:01:49,658 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-06-23 13:01:49,663 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-23 13:01:49,677 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-23 13:01:49,688 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-06-23 13:01:49,693 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-06-23 13:01:49,699 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-06-23 13:01:49,704 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-06-23 13:01:49,841 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-06-23 13:01:49,844 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-06-23 13:01:49,848 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-23 13:01:49,851 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-23 13:01:49,864 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-06-23 13:01:49,868 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for child_ngo
2025-06-23 13:01:49,872 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-06-23 13:01:49,875 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for child_ngo
2025-06-23 13:01:49,880 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-06-23 13:01:49,883 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for child_ngo
2025-06-23 13:01:49,887 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-06-23 13:01:49,891 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-23 13:01:49,895 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-23 13:01:49,898 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-23 13:01:49,901 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-23 13:01:49,905 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-23 13:01:49,908 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-23 13:01:49,912 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-23 13:01:49,915 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-23 13:01:49,919 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-23 13:01:49,922 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-06-23 13:01:49,925 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-23 13:01:49,929 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-23 13:01:49,936 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-23 13:01:49,942 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-24 13:50:00,823 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-24 13:50:00,827 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-24 13:50:00,830 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-24 13:50:00,833 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-24 13:50:00,843 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-24 13:50:00,846 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-24 13:50:00,851 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-24 13:50:00,855 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-24 13:50:00,865 ERROR scheduler Skipped queueing ngo.utils.loan.apply_late_payment_fees because it was found in queue for child_ngo
2025-06-24 13:50:00,870 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-24 13:50:00,878 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-24 13:50:00,938 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-24 13:50:00,941 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-24 13:50:00,944 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-24 13:50:00,947 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-24 13:50:00,951 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-24 13:50:00,954 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-24 13:50:00,959 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-24 13:50:01,011 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-24 13:50:01,014 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-24 13:50:01,016 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-24 13:50:01,019 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-24 13:50:01,021 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-24 13:50:01,024 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-24 13:50:01,026 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-24 13:50:01,029 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-24 13:50:01,032 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-24 13:50:01,036 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-24 13:50:01,039 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-24 13:50:01,042 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-24 13:50:01,045 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-24 20:31:21,833 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-24 20:31:21,837 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-24 20:31:21,852 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-24 20:31:21,855 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-24 20:31:21,858 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-24 20:31:21,861 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-24 20:31:21,959 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-24 20:31:21,961 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-24 20:31:21,966 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-24 20:31:22,051 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-24 20:31:22,055 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-24 20:31:22,058 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-24 20:31:22,061 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-25 11:24:05,671 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-25 11:24:05,677 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-25 11:24:05,680 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-25 11:24:05,683 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-25 11:24:05,690 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-06-25 11:24:05,695 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for child_ngo
2025-06-25 11:24:05,700 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-06-25 11:24:05,706 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-25 11:24:05,711 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-25 11:24:05,715 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-25 11:24:05,723 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for child_ngo
2025-06-25 11:24:05,728 ERROR scheduler Skipped queueing ngo.utils.loan.apply_late_payment_fees because it was found in queue for child_ngo
2025-06-25 11:24:05,732 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-06-25 11:24:05,737 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-25 11:24:05,740 ERROR scheduler Skipped queueing ngo.utils.attachments.set_attachments_status because it was found in queue for child_ngo
2025-06-25 11:24:05,747 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-25 11:24:05,752 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-06-25 11:24:05,755 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-06-25 11:24:05,759 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-06-25 11:24:05,762 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-06-25 11:24:05,765 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for child_ngo
2025-06-25 11:24:05,768 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for child_ngo
2025-06-25 11:24:05,771 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for child_ngo
2025-06-25 11:24:05,773 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for child_ngo
2025-06-25 11:24:05,777 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for child_ngo
2025-06-25 11:24:05,780 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for child_ngo
2025-06-25 11:24:05,783 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for child_ngo
2025-06-25 11:24:05,786 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for child_ngo
2025-06-25 11:24:05,789 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for child_ngo
2025-06-25 11:24:05,792 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for child_ngo
2025-06-25 11:24:05,795 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for child_ngo
2025-06-25 11:24:05,798 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for child_ngo
2025-06-25 11:24:05,801 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for child_ngo
2025-06-25 11:24:05,804 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for child_ngo
2025-06-25 11:24:05,807 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for child_ngo
2025-06-25 11:24:05,810 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for child_ngo
2025-06-25 11:24:05,813 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for child_ngo
2025-06-25 11:24:05,816 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for child_ngo
2025-06-25 11:24:05,819 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for child_ngo
2025-06-25 11:24:05,822 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for child_ngo
2025-06-25 11:24:05,825 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for child_ngo
2025-06-25 11:24:05,827 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-25 11:24:05,830 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-25 11:24:05,833 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-25 11:24:05,837 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-25 11:24:05,840 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for child_ngo
2025-06-25 11:24:05,845 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-25 11:24:05,849 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for child_ngo
2025-06-25 11:24:05,852 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-25 11:24:05,875 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-06-25 11:24:05,879 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-06-25 11:24:05,882 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-25 11:24:05,885 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-25 11:24:05,888 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.set_auto_repeat_as_completed because it was found in queue for child_ngo
2025-06-25 11:24:05,891 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-06-25 11:24:05,894 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-06-25 11:24:05,897 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-06-25 11:24:05,899 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for child_ngo
2025-06-25 11:24:05,902 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-06-25 11:24:05,905 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for child_ngo
2025-06-25 11:24:05,908 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-06-25 11:24:05,911 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for child_ngo
2025-06-25 11:24:05,914 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-06-25 11:24:05,917 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-25 11:24:05,920 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-25 11:24:05,923 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-25 11:24:05,926 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-25 11:24:05,929 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-25 11:24:05,932 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-25 11:24:05,936 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-25 11:24:05,944 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-25 11:24:05,948 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-25 11:24:05,951 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-06-25 11:24:05,954 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-25 11:24:05,957 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-25 11:24:05,960 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-25 11:24:05,963 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-25 11:25:46,252 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-06 12:18:12,789 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-07-06 12:18:12,917 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-07-08 11:21:14,725 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-07-08 11:21:14,733 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-08 11:21:14,782 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-08 11:21:14,809 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-08 11:21:14,825 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-07-08 11:21:14,827 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-08 11:21:14,837 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-07-08 11:21:14,843 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-07-08 11:21:14,847 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-07-08 11:21:14,849 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-08 11:21:14,886 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-08 11:21:14,953 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-07-08 11:21:14,955 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-08 11:21:14,956 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-08 11:21:14,966 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-08 11:21:15,029 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-08 11:21:15,054 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-07-08 11:21:15,056 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-08 11:21:15,057 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-07-08 11:21:15,060 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-08 11:21:15,068 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-08 11:21:15,078 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-08 11:21:15,081 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-08 11:21:15,082 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-08 11:21:15,085 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-08 11:21:15,086 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-08 11:21:15,089 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-08 11:21:15,091 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-13 11:18:52,419 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-13 11:18:52,423 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-13 11:18:52,425 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-13 11:18:52,439 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-07-13 11:18:52,522 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-07-13 11:18:52,529 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for child_ngo
2025-07-13 11:18:52,533 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for child_ngo
2025-07-13 11:18:52,561 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-07-13 11:18:52,568 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-13 11:18:52,571 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-13 11:18:52,573 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for child_ngo
2025-07-13 11:18:52,587 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for child_ngo
2025-07-13 11:18:52,593 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for child_ngo
2025-07-13 11:18:52,596 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-13 11:18:52,604 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for child_ngo
2025-07-13 11:18:52,606 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-13 11:18:52,625 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for child_ngo
2025-07-13 11:18:52,633 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-13 11:18:52,635 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-07-13 11:18:52,641 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-13 11:18:52,651 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-13 11:18:52,656 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-13 11:18:52,676 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-07-13 11:18:52,678 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-07-13 11:18:52,681 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-13 11:18:52,684 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-13 11:18:52,690 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-07-13 11:18:52,693 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-13 11:18:52,696 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-13 11:18:52,704 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-13 11:18:52,708 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-13 11:18:52,711 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-13 11:18:52,715 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-13 11:18:52,724 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-13 11:18:52,732 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-07-20 11:56:10,313 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-20 11:56:10,316 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-20 11:56:10,321 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-20 11:56:10,325 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-20 11:56:10,330 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-20 11:56:10,334 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for child_ngo
2025-07-20 11:56:10,344 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for child_ngo
2025-07-20 11:56:10,348 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-20 11:56:10,353 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for child_ngo
2025-07-20 11:56:10,358 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-20 11:56:10,362 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-20 11:56:10,367 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-20 11:56:10,372 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-20 11:56:10,386 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-20 11:56:10,393 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-20 11:56:10,396 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-20 11:56:10,400 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-20 11:56:10,405 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-20 11:56:10,407 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-20 11:56:10,409 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-20 11:56:10,411 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for child_ngo
2025-07-20 11:56:10,414 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for child_ngo
2025-07-20 11:56:10,430 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-28 14:35:19,610 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-28 14:35:19,620 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-28 14:35:19,632 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-07-28 14:35:19,639 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-07-28 14:35:19,651 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-28 14:35:19,657 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-28 14:35:19,664 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-07-28 14:35:19,667 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-28 14:35:19,680 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-28 14:35:19,686 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-28 14:35:19,694 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-28 14:35:19,698 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-28 14:35:19,715 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-28 14:35:19,721 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-28 14:35:19,750 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-28 14:35:19,754 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-28 14:35:19,765 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-28 14:35:19,768 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-28 14:35:19,789 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-28 14:35:19,805 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-28 14:35:19,814 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-07-28 14:35:19,821 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-07-28 14:35:19,825 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-07-28 14:35:19,837 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-07-28 14:35:19,843 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-28 14:35:19,852 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-07-28 14:35:19,863 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-28 14:35:19,880 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-28 14:36:22,055 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-08-01 00:01:52,192 ERROR scheduler Skipped queueing erpnext.accounts.deferred_revenue.process_deferred_accounting because it was found in queue for child_ngo
2025-08-01 00:01:52,195 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-08-01 00:01:52,200 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-08-01 00:01:52,202 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-08-01 00:01:52,208 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-08-01 00:01:52,211 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-08-01 00:01:52,214 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-08-01 00:01:52,220 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-08-01 00:01:52,223 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-08-01 00:01:52,229 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for child_ngo
2025-08-01 00:01:52,234 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-08-01 00:01:52,237 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-08-01 00:01:52,240 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-08-01 00:01:52,245 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-08-01 00:01:52,264 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-08-01 00:01:52,274 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-08-01 00:01:52,295 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-08-01 00:01:52,300 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-08-01 00:01:52,304 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-08-01 00:01:52,313 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_monthly because it was found in queue for child_ngo
2025-08-01 00:01:52,318 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-08-01 00:01:52,320 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-08-01 00:01:52,323 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-08-01 00:01:52,325 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-08-01 00:01:52,328 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-08-01 00:01:52,332 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-08-01 00:01:52,333 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-08-01 00:01:52,338 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-08-01 00:01:52,346 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-08-01 00:01:52,349 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-08-01 00:01:52,352 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-08-05 14:38:01,554 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-08-05 14:38:01,561 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-08-05 14:38:01,563 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-08-05 14:38:01,572 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-08-05 14:38:01,575 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-08-05 14:38:01,580 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-08-05 14:38:01,589 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-08-05 14:38:01,595 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-08-05 14:38:01,612 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-08-05 14:38:01,626 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-08-05 14:38:01,632 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-08-05 14:38:01,635 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-08-05 14:38:01,639 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-08-05 14:38:01,647 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-08-05 14:38:01,650 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-08-05 14:38:01,668 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-08-05 14:38:01,673 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-08-05 14:38:01,677 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-08-05 14:38:01,681 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-08-05 14:38:01,692 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-08-05 14:38:01,694 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-08-05 14:38:01,697 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-08-05 14:38:01,706 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-08-05 14:38:01,723 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-08-05 14:38:01,736 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-08-05 14:38:01,742 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-08-05 14:38:01,745 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-08-05 14:38:01,754 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-08-06 15:01:39,992 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-08-06 15:01:40,103 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-08-06 15:01:40,142 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-08-06 15:01:40,168 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-08-06 15:01:40,172 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-08-06 15:01:40,177 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-08-06 15:01:40,304 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-08-06 15:01:40,308 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-08-06 15:01:40,315 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-08-06 15:01:40,333 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-08-07 12:26:52,621 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
