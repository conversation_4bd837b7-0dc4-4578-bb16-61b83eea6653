["tabPortal Menu Item", "tabList View Settings", "tabAdministrative Follow Up", "tabWorkspace Link", "tabWorkspace Number Card", "tabHas Role", "tabParty", "tabWorkflow Transition", "tabDocType State", "tabWorkflow", "tabWorkflow Action Master", "tabLoan Request", "tabWorkflow State", "tabWorkspace", "tabIAM Role", "tabLoan", "tabSeries", "tabFinancial", "tabLR", "tabPolicy", "tabProperty Setter", "tabDocType Action", "tabCustom", "tabDocField", "tabAttachment", "tabLR Board Member Details", "tabDocType", "tabInstalled Application", "tabIAM", "tabEmail Template", "tabWebsite", "tabAdministrative Investigation", "tabVersion", "tabInstalled", "tabRole Workspace", "tabLoan Disbursement Request", "tabAPI", "tabWorkspace Shortcut", "tabCustomer", "tabCustom Field", "tabEmail", "tabPrint Heading", "tabAPI Response", "tabWebsite Route Redirect", "tabWorkspace Chart", "tabHas", "tabProperty", "tabPortal", "tabNavbar <PERSON>em", "tabWorkspace Custom Block", "tabSingles", "tabTag", "tabRoute", "tabParty Type", "tabNavbar", "tabCustom DocPerm", "tabCustomer NGO", "tabRoute History", "tabList", "tabTop Bar Item", "tabTop", "tabDocPerm", "tabTag Link", "tabRole Profile", "tabWorkflow Document State", "tabFinancial Investigation", "tabPrint", "tabAPI Response Target", "tabDocType Link", "tabWorkspace Quick List", "tabRole", "tabCustom Role", "tabAdministrative", "tabFinancial Follow Up"]