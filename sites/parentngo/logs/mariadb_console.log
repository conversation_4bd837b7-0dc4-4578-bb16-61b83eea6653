_HiStOrY_V2_
Select\040*\040from\040tabLoan
Select\040*\040from\040tabLoan;
Select\040repayment_schedule\040from\040tabLoan;
Select\040is_paid\040from\040tabLoan;
Select\040repayment_schedule\040from\040tabLoan;
\040\040\040\040SELECT
\040\040\040\040\040\040\040\040loan.name,loan.ngo_name,loan.repayment_start_date,loan.monthly_repayment_amount,loan.status\040\040\040\040\040\040\040\040\040\040\040\040
\040\040\040\040\040\040\040\040FROM\040`tabLoan`\040loan
\040\040\040\040\040\040\040\040JOIN\040`tabRepayment\040Schedule`\040repayment_schedule\040
\040\040\040\040\040\040\040\040ON\040loan.name\040=\040repayment_schedule.parent;
SELECT\040\040\040\040\040\040\040\040\040loan.name,loan.ngo_name,loan.repayment_start_date,loan.monthly_repayment_amount,loan.status\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040FROM\040`tabLoan`\040loan\040\040\040\040\040\040\040\040\040JOIN\040`tabRepayment\040Schedule`\040repayment_schedule\040\040\040\040\040\040\040\040\040\040ON\040loan.name\040=\040repayment_schedule.parent;
SELECT\040\040\040\040\040\040\040\040\040loan.name,loan.ngo_name,loan.repayment_start_date,loan.monthly_repayment_amount,repayment_schedule.is_paid\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040FROM\040`tabLoan`\040loan\040\040\040\040\040\040\040\040\040JOIN\040`tabRepayment\040Schedule`\040repayment_schedule\040\040\040\040\040\040\040\040\040\040ON\040loan.name\040=\040repayment_schedule.parent;
SELECT\040\040\040repayment_schedule.is_paid\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040FROM\040`tabLoan`\040loan\040\040\040\040\040\040\040\040\040JOIN\040`tabRepayment\040Schedule`\040repayment_schedule\040\040\040\040\040\040\040\040\040\040ON\040loan.name\040=\040repayment_schedule.parent;
\040\040\040\040SELECT
\040\040\040\040\040\040\040\040loan.name\040as\040name,\040loan.ngo_name\040as\040ngo_name,\040repayment_schedule.payment_date\040as\040payment_date,\040repayment_schedule.total_amount\040as\040total_amount,repayment_schedule.is_paid\040as\040is_paid\040\040\040\040\040\040\040\040\040\040\040
\040\040\040\040\040\040\040\040FROM\040`tabLoan`\040loan
\040\040\040\040\040\040\040\040JOIN\040`tabRepayment\040Schedule`\040repayment_schedule\040
\040\040\040\040\040\040\040\040ON\040loan.name\040=\040repayment_schedule.parent;
SELECT\040\040\040\040\040\040\040\040\040loan.name\040as\040name,\040loan.ngo_name\040as\040ngo_name,\040repayment_schedule.payment_date\040as\040payment_date,\040repayment_schedule.total_amount\040as\040total_amount,repayment_schedule.is_paid\040as\040is_paid\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040\040FROM\040`tabLoan`\040loan\040\040\040\040\040\040\040\040\040JOIN\040`tabRepayment\040Schedule`\040repayment_schedule\040\040\040\040\040\040\040\040\040\040ON\040loan.name\040=\040repayment_schedule.parent;
SHOW\040COLUMNS\040FROM\040`tabFinancial\040Investigation`;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`financial_records_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`final_accounts_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`financial_cycle_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
SHOW\040COLUMNS\040FROM\040`tabFinancial\040Investigation`;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`financial_cycle_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`final_accounts_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`financial_records_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
SHOW\040COLUMNS\040FROM\040`tabLoan\040Disbursement`;
SELECT\040signed_and_stamped_repayment_schedule\040FROM\040`tabLoan\040Disbursement`;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`financial_cycle_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`final_accounts_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
ALTER\040TABLE\040`tabFinancial\040Investigation`\040MODIFY\040COLUMN\040`financial_records_rating`\040DECIMAL(3,2)\040DEFAULT\0400;
SHOW\040COLUMNS\040FROM\040`tabCustomer\040NGO\040Attachments`;
SELECT\040*\040FROM\040`tabLR\040Board\040Member\040Details`\040WHERE\040name\040=\040"ns9h7tmpcb";
SELECT\040name,parent\040FROM\040`tabLR\040Board\040Member\040Details`\040WHERE\040name\040=\040"ns9h7tmpcb";
SELECT\040name,parent,bm_iscore_status\040FROM\040`tabLR\040Board\040Member\040Details`\040WHERE\040name\040=\040"ns9h7tmpcb";
SELECT\040name,parent,bm_iscore_status\040FROM\040`tabLR\040Board\040Member\040Details`\040WHERE\040name\040=\040"3t1frfec2k";
SELECT\040name,parent,bm_iscore_status\040FROM\040`tabLoan\040Request`;
SELECT\040*\040FROM\040`tabLoan\040Request`\040WHERE\040name\040=\040"LR-56";
SELECT\040treasurer_iscore_status\040FROM\040`tabLoan\040Request`\040WHERE\040name\040=\040"LR-56";
DELETE\040t1\040FROM\040`tabCustomer\040NGO`\040t1
INNER\040JOIN\040`tabCustomer\040NGO`\040t2\040
WHERE\040
\040\040t1.name\040>\040t2.name\040AND\040
\040\040t1.contact_nid\040=\040t2.contact_nid;
DELETE\040t1\040FROM\040`tabCustomer\040NGO`\040t1\040INNER\040JOIN\040`tabCustomer\040NGO`\040t2\040\040WHERE\040\040\040\040t1.name\040>\040t2.name\040AND\040\040\040\040t1.contact_nid\040=\040t2.contact_nid;
SELECT\040contact_nid,\040COUNT(*)\040
FROM\040`tabCustomer\040NGO`\040
GROUP\040BY\040contact_nid\040
HAVING\040COUNT(*)\040>\0401;
SELECT\040contact_nid,\040COUNT(*)\040\040FROM\040`tabCustomer\040NGO`\040\040GROUP\040BY\040contact_nid\040\040HAVING\040COUNT(*)\040>\0401;
DELETE\040FROM\040`tabCustomer\040NGO`\040\040WHERE\040contact_nid\040=\04028001019992999;
SELECT\040name\040FROM\040`tabCustomer\040NGO`\040WHERE\040contact_nid\040=\04028001019992999;
SET\040SQL_SAFE_UPDATES\040=\0400;
DELETE\040FROM\040`tabCustomer\040NGO`\040WHERE\040contact_nid\040=\04028001019992999;
SET\040SQL_SAFE_UPDATES\040=\0401;
SET\040SQL_SAFE_UPDATES\040=\0400;
DELETE\040FROM\040`tabCustomer\040NGO`\040WHERE\040contact_nid\040=\04028808141401447;
DELETE\040FROM\040`tabCustomer\040NGO`\040WHERE\040contact_nid\040=\04029001019889888;
SET\040SQL_SAFE_UPDATES\040=\0401;
