2025-06-25 11:25:10,476 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` ADD COLUMN `letter_head` varchar(140)
2025-06-25 11:25:10,495 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` <PERSON>ODIFY `sample_size` decimal(21,9) not null default 0
2025-06-25 11:25:11,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0
2025-06-25 11:25:11,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-25 11:25:11,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-06-25 11:25:11,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `recreate_stock_ledgers` int(1) not null default 0
2025-06-25 11:25:11,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD INDEX `creation`(`creation`)
2025-06-25 11:25:12,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `amount_difference_with_purchase_invoice` decimal(21,9) not null default 0
2025-06-25 11:25:12,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0
2025-06-25 11:25:12,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `sla_resolution_by` datetime(6), ADD COLUMN `sla_resolution_date` datetime(6)
2025-06-25 11:25:12,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `total_hold_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9)
2025-06-25 11:25:12,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` ADD COLUMN `difference_account` varchar(140)
2025-06-25 11:25:12,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `current_asset_value` decimal(21,9) not null default 0, MODIFY `new_asset_value` decimal(21,9) not null default 0
2025-06-25 11:25:13,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` ADD COLUMN `non_depreciable_category` int(1) not null default 0
2025-06-25 11:25:13,354 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0
2025-06-25 11:25:13,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD COLUMN `purchase_receipt_item` varchar(140)
2025-06-25 11:25:13,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-06-25 11:25:13,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD INDEX `creation`(`creation`)
2025-06-25 11:25:13,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` ADD COLUMN `subcontracting_conversion_factor` decimal(21,9) not null default 0, ADD COLUMN `job_card` varchar(140)
2025-06-25 11:25:13,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `service_cost_per_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `rm_cost_per_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-25 11:25:14,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-25 11:25:14,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `current_stock` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-25 11:25:14,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-06-25 11:25:14,937 WARNING database DDL Query made to DB:
ALTER TABLE __UserSettings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
2025-06-25 11:25:15,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-25 11:25:15,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_warehouse`
2025-07-01 14:21:35,415 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-01 14:21:36,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-01 14:21:37,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-01 14:21:37,974 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-07-01 14:21:38,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepayment Schedule` MODIFY `principal_amount` decimal(21,9) not null default 0, MODIFY `late_payment_fees_rate` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-07-01 14:21:38,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `principal_amount` decimal(21,9) not null default 0, MODIFY `late_payment_fees` decimal(21,9) not null default 0
2025-07-01 14:21:38,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Disbursement` MODIFY `last_semi_annual_principal_amount` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `semi_annual_principal_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_interest_amount` decimal(21,9) not null default 0
2025-07-01 14:21:51,283 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-01 14:21:52,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-01 14:21:53,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-01 14:21:54,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-07-17 17:25:13,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `late_payment_fees_rate` decimal(21,9) not null default 0
2025-07-17 17:26:32,710 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `remaining_interest_amount` decimal(21,9) not null default 0, MODIFY `remaining_principal_amount` decimal(21,9) not null default 0, MODIFY `remaining_late_payment_fees` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0, MODIFY `undisbursed_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0
2025-07-17 17:27:13,501 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Disbursement` MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `semi_annual_principal_amount` decimal(21,9) not null default 0, MODIFY `last_semi_annual_principal_amount` decimal(21,9) not null default 0
2025-07-17 17:27:24,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0, MODIFY `remaining_interest_amount` decimal(21,9) not null default 0, MODIFY `remaining_principal_amount` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `remaining_late_payment_fees` decimal(21,9) not null default 0, MODIFY `undisbursed_amount` decimal(21,9) not null default 0
2025-07-17 17:28:55,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` MODIFY `late_payment_fees` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `principal_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-07-21 12:01:55,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` ADD COLUMN `posting_date` date
2025-07-21 12:01:55,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `principal_amount` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `late_payment_fees` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0
2025-07-22 13:51:55,854 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-22 13:51:56,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-22 13:51:57,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-22 13:51:59,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-07-22 13:51:59,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` MODIFY `late_payment_fees` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `principal_amount` decimal(21,9) not null default 0
2025-07-22 14:43:59,623 WARNING database DDL Query made to DB:
alter table `tabFuture Disbursement` add column if not exists parent varchar(140)
2025-07-22 14:43:59,624 WARNING database DDL Query made to DB:
alter table `tabFuture Disbursement` add column if not exists parenttype varchar(140)
2025-07-22 14:43:59,625 WARNING database DDL Query made to DB:
alter table `tabFuture Disbursement` add column if not exists parentfield varchar(140)
2025-07-22 14:43:59,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuture Disbursement` MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-23 11:58:45,526 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-23 11:58:46,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart` ADD COLUMN `show_values_over_chart` int(1) not null default 0
2025-07-23 11:58:47,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-23 11:58:47,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD COLUMN `company` varchar(140)
2025-07-23 11:58:47,529 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD INDEX `creation`(`creation`)
2025-07-23 11:58:48,089 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-07-23 11:58:49,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0
2025-07-23 11:58:50,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity` MODIFY `first_response_time` decimal(21,9), MODIFY `opportunity_amount` decimal(21,9) not null default 0, MODIFY `annual_revenue` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_opportunity_amount` decimal(21,9) not null default 0
2025-07-23 11:58:50,521 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-07-23 11:58:50,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `subject` varchar(140)
2025-07-23 11:58:50,929 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-07-23 11:58:51,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-23 11:58:51,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-23 11:58:51,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `batch_size` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-07-23 11:58:52,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `disassembled_qty` decimal(21,9) not null default 0
2025-07-23 11:58:52,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0
2025-07-23 11:58:52,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD COLUMN `delivered_by_supplier` int(1) not null default 0
2025-07-23 11:58:52,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-07-23 11:58:52,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `delivery_status` varchar(140), ADD COLUMN `per_delivered` decimal(21,9) not null default 0
2025-07-23 11:58:52,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-07-23 11:58:53,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-07-23 11:58:53,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` ADD COLUMN `customer` varchar(140)
2025-07-23 11:58:53,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` MODIFY `purchase_rate` decimal(21,9) not null default 0
2025-07-23 11:58:54,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `delivered_qty` decimal(21,9) not null default 0
2025-07-23 11:58:54,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-07-23 11:58:54,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `buying_price_list` varchar(140)
2025-07-23 11:58:54,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-23 11:58:55,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `against_pick_list` varchar(140)
2025-07-23 11:58:55,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-23 11:58:55,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `against_pick_list_index`(`against_pick_list`)
2025-07-23 11:58:55,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-07-23 11:58:55,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement` ADD INDEX `creation`(`creation`)
2025-07-23 11:58:56,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-07-23 11:58:56,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuture Disbursement` MODIFY `remaining_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-23 11:58:56,947 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan` MODIFY `remaining_interest_amount` decimal(21,9) not null default 0, MODIFY `remaining_principal_amount` decimal(21,9) not null default 0, MODIFY `undisbursed_amount` decimal(21,9) not null default 0, MODIFY `remaining_late_payment_fees` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0, MODIFY `remaining_amount` decimal(21,9) not null default 0
2025-07-23 11:58:57,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-07-23 11:58:57,455 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Request` MODIFY `ceo_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `treasurer_iscore_total_balance` decimal(21,9) not null default 0, MODIFY `interest_rate` decimal(21,9) not null default 0, MODIFY `late_payment_fees_rate` decimal(21,9) not null default 0
2025-07-23 11:58:57,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Disbursement` MODIFY `last_semi_annual_principal_amount` decimal(21,9) not null default 0, MODIFY `last_monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `monthly_interest_amount` decimal(21,9) not null default 0, MODIFY `semi_annual_principal_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0
2025-07-23 14:42:51,991 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-23 14:42:52,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-23 14:42:53,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-23 14:42:54,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-07-23 17:48:02,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` MODIFY `principal_amount` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `late_payment_fees` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-08-04 11:33:10,648 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-04 11:33:12,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-04 11:33:13,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-04 11:33:14,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdministrative Investigation` ADD COLUMN `manager` varchar(140) default 'Found', ADD COLUMN `accountant` varchar(140) default 'Found', ADD COLUMN `officer` varchar(140) default 'Found', ADD COLUMN `cashier` varchar(140) default 'Found', ADD COLUMN `social_investigator` varchar(140) default 'Found'
2025-08-04 11:33:15,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-08-04 11:33:15,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `late_payment_fees` decimal(21,9) not null default 0, MODIFY `principal_amount` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-08-04 15:28:13,253 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-04 15:28:14,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-04 15:28:15,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-04 15:28:16,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-08-05 14:36:04,004 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-05 14:36:05,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-05 14:36:06,286 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-05 14:36:07,346 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-08-05 14:47:15,426 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-05 14:47:16,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-05 14:47:17,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-05 14:47:18,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-08-05 15:29:10,575 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-05 15:29:11,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-05 15:29:13,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-05 15:29:14,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-08-06 11:30:04,902 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-06 11:30:05,851 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-06 11:30:06,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-06 11:30:07,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-08-06 17:46:25,859 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-06 17:46:26,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-06 17:46:27,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-06 17:46:28,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
2025-08-07 12:34:10,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer NGO` ADD UNIQUE INDEX IF NOT EXISTS contact_nid (`contact_nid`), ADD UNIQUE INDEX IF NOT EXISTS contact_mobile_no (`contact_mobile_no`), ADD UNIQUE INDEX IF NOT EXISTS treasurer_nid (`treasurer_nid`), ADD UNIQUE INDEX IF NOT EXISTS treasurer_mobile_no (`treasurer_mobile_no`), ADD UNIQUE INDEX IF NOT EXISTS ceo_nid (`ceo_nid`), ADD UNIQUE INDEX IF NOT EXISTS ceo_mobile_no (`ceo_mobile_no`)
2025-08-07 12:35:18,742 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-07 12:35:19,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-07 12:35:20,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-07 12:35:21,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabLR Board Member Details` MODIFY `bm_iscore_total_balance` decimal(21,9) not null default 0
