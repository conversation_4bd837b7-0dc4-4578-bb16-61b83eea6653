{"billing.bundle.js": "/assets/frappe/dist/js/billing.bundle.3M2NKQ7F.js", "bootstrap-4-web.bundle.js": "/assets/frappe/dist/js/bootstrap-4-web.bundle.AZ67VXZX.js", "controls.bundle.js": "/assets/frappe/dist/js/controls.bundle.KVMVPF6Y.js", "data_import_tools.bundle.js": "/assets/frappe/dist/js/data_import_tools.bundle.FXJ6JQBT.js", "desk.bundle.js": "/assets/frappe/dist/js/desk.bundle.FQXMYJR6.js", "dialog.bundle.js": "/assets/frappe/dist/js/dialog.bundle.X7QCPWYD.js", "form.bundle.js": "/assets/frappe/dist/js/form.bundle.FC4IJHPH.js", "frappe-web.bundle.js": "/assets/frappe/dist/js/frappe-web.bundle.4XKJFVOE.js", "libs.bundle.js": "/assets/frappe/dist/js/libs.bundle.LLRFRX7M.js", "list.bundle.js": "/assets/frappe/dist/js/list.bundle.D4XCKPVV.js", "logtypes.bundle.js": "/assets/frappe/dist/js/logtypes.bundle.MJKW7EK3.js", "onboarding_tours.bundle.js": "/assets/frappe/dist/js/onboarding_tours.bundle.P7QYMXLW.js", "report.bundle.js": "/assets/frappe/dist/js/report.bundle.VWKAH5FV.js", "sentry.bundle.js": "/assets/frappe/dist/js/sentry.bundle.SI3DB3BY.js", "telemetry.bundle.js": "/assets/frappe/dist/js/telemetry.bundle.ZJBT5ETW.js", "user_profile_controller.bundle.js": "/assets/frappe/dist/js/user_profile_controller.bundle.TAMQL3L3.js", "video_player.bundle.js": "/assets/frappe/dist/js/video_player.bundle.IOEIXC2G.js", "web_form.bundle.js": "/assets/frappe/dist/js/web_form.bundle.LIVMNRCL.js", "form_builder.bundle.js": "/assets/frappe/dist/js/form_builder.bundle.J2JYNB4Y.js", "print_format_builder.bundle.js": "/assets/frappe/dist/js/print_format_builder.bundle.OYD36SWC.js", "workflow_builder.bundle.js": "/assets/frappe/dist/js/workflow_builder.bundle.PXAJNMY7.js", "build_events.bundle.js": "/assets/frappe/dist/js/build_events.bundle.AOI2Q2ON.js", "file_uploader.bundle.js": "/assets/frappe/dist/js/file_uploader.bundle.W324BGHU.js", "kanban_board.bundle.js": "/assets/frappe/dist/js/kanban_board.bundle.OUFA2R27.js", "controls_pngo.bundle.js": "/assets/parent_ngo/dist/js/controls_pngo.bundle.63WKTKQ3.js", "reports_pngo.bundle.js": "/assets/parent_ngo/dist/js/reports_pngo.bundle.S55P4HU3.js", "ui_pngo.bundle.js": "/assets/parent_ngo/dist/js/ui_pngo.bundle.L2VWCDID.js", "views_pngo.bundle.js": "/assets/parent_ngo/dist/js/views_pngo.bundle.5H4KFOBK.js", "workflow_pngo.bundle.js": "/assets/parent_ngo/dist/js/workflow_pngo.bundle.CA2RJ5GX.js", "breadcrumbs.bundle.js": "/assets/iam/dist/js/breadcrumbs.bundle.3TQVL6CV.js", "form_iam.bundle.js": "/assets/iam/dist/js/form_iam.bundle.P2DEHAA5.js", "frappe_iam.bundle.js": "/assets/iam/dist/js/frappe_iam.bundle.LDGORSF4.js", "attachments_ngo.bundle.js": "/assets/ngo/dist/js/attachments_ngo.bundle.AEABB3O3.js", "controls_ngo.bundle.js": "/assets/ngo/dist/js/controls_ngo.bundle.IS3RMF7X.js", "reports_ngo.bundle.js": "/assets/ngo/dist/js/reports_ngo.bundle.33H4ARMP.js", "ui_ngo.bundle.js": "/assets/ngo/dist/js/ui_ngo.bundle.EX2F4MHU.js", "views_ngo.bundle.js": "/assets/ngo/dist/js/views_ngo.bundle.OT2HI747.js", "workflow_ngo.bundle.js": "/assets/ngo/dist/js/workflow_ngo.bundle.XQISXHG6.js", "website.bundle.js": "/assets/lms/dist/js/website.bundle.GAMTVV3F.js", "bank-reconciliation-tool.bundle.js": "/assets/erpnext/dist/js/bank-reconciliation-tool.bundle.G7MHMAFO.js", "erpnext-web.bundle.js": "/assets/erpnext/dist/js/erpnext-web.bundle.253I7LT4.js", "erpnext.bundle.js": "/assets/erpnext/dist/js/erpnext.bundle.5JHQIMVT.js", "item-dashboard.bundle.js": "/assets/erpnext/dist/js/item-dashboard.bundle.6HBERHEF.js", "point-of-sale.bundle.js": "/assets/erpnext/dist/js/point-of-sale.bundle.WOTPRTPD.js", "bom_configurator.bundle.js": "/assets/erpnext/dist/js/bom_configurator.bundle.PLBYQFQX.js", "desk.bundle.css": "/assets/frappe/dist/css/desk.bundle.ITHMO65D.css", "email.bundle.css": "/assets/frappe/dist/css/email.bundle.4E7LQEX3.css", "login.bundle.css": "/assets/frappe/dist/css/login.bundle.N3GC624W.css", "print.bundle.css": "/assets/frappe/dist/css/print.bundle.AH4TQQUN.css", "print_format.bundle.css": "/assets/frappe/dist/css/print_format.bundle.LJ3TUBC7.css", "report.bundle.css": "/assets/frappe/dist/css/report.bundle.6EQ3H3YN.css", "web_form.bundle.css": "/assets/frappe/dist/css/web_form.bundle.G756U3TC.css", "website.bundle.css": "/assets/frappe/dist/css/website.bundle.A247FWBS.css", "datavalue_theme.bundle.css": "/assets/datavalue_theme_15/dist/css/datavalue_theme.bundle.MHZUBMET.css", "lms.bundle.css": "/assets/lms/dist/css/lms.bundle.XIZPQC45.css", "erpnext-web.bundle.css": "/assets/erpnext/dist/css/erpnext-web.bundle.TAYMP6VF.css", "erpnext.bundle.css": "/assets/erpnext/dist/css/erpnext.bundle.BH3OUCER.css", "erpnext_email.bundle.css": "/assets/erpnext/dist/css/erpnext_email.bundle.S7RS7GCG.css", "hierarchy-chart.bundle.js": "/assets/hrms/dist/js/hierarchy-chart.bundle.5VLQOKCT.js", "hrms.bundle.js": "/assets/hrms/dist/js/hrms.bundle.LALKTF6X.js", "interview.bundle.js": "/assets/hrms/dist/js/interview.bundle.3XEQBIPT.js", "performance.bundle.js": "/assets/hrms/dist/js/performance.bundle.KS2NASJ3.js", "hrms.bundle.css": "/assets/hrms/dist/css/hrms.bundle.UCOKF44C.css"}